import { expect, test, beforeAll, afterAll, beforeEach } from 'vitest';
import { setupElectronTest, cleanupElectronTest, type ElectronTestContext, waitForOperation, createTestFile, openFile } from '../helpers/electron-setup';
import { getSyncMetadata, waitForSuccessNotice } from '../helpers/plugin-setup';
import * as fs from 'fs';
import * as path from 'path';

/**
 * E2E Tests for Sync Current Post to Ghost - Ported to Playwright/Electron
 *
 * These tests verify the main sync functionality that users would use most:
 * 1. Sync current post to <PERSON> via command palette
 * 2. Sync current post to <PERSON> via ribbon icon
 * 3. Sync current post to <PERSON> via direct command
 * 4. Handle posts without slugs
 * 5. Handle new posts vs existing posts
 * 6. Verify sync metadata is updated after successful sync
 */

describe("Ghost Sync - Sync Current Post E2E Tests (Playwright/Electron)", () => {
  let context: ElectronTestContext;
  const articlesDir = path.resolve('./tests/vault/Test/articles');

  beforeAll(async () => {
    context = await setupElectronTest();
  });

  afterAll(async () => {
    await cleanupElectronTest(context);
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('sync-current-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }
    await waitForOperation(200);
  });

  test("should sync current post to Ghost via command palette", async () => {
    const testTitle = "Sync Current Test Post";
    const testSlug = "sync-current-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post for syncing current post to Ghost via command palette.

## Test Content

Some content to verify the sync works correctly.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Open command palette and sync current post
    await context.page.keyboard.down('Meta'); // Cmd on Mac
    await context.page.keyboard.press('KeyP');
    await context.page.keyboard.up('Meta');

    // Type the sync command
    await context.page.keyboard.type('Sync current post to Ghost');
    await context.page.keyboard.press('Enter');

    console.log("Executed sync current post command via command palette");

    // Wait for success notice to appear
    const successNoticeAppeared = await waitForSuccessNotice(context.page, 5000);
    expect(successNoticeAppeared).toBe(true);

    // Verify sync metadata was updated
    const syncMetadata = await getSyncMetadata(context.page, relativeFilePath);
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata.ghostId).toBeTruthy();
    expect(syncMetadata.syncedAt).toBeTruthy();

    console.log(`✅ Post synced successfully. Ghost ID: ${syncMetadata.ghostId}`);
  });

  test("should sync current post to Ghost via ribbon icon", async () => {
    const testTitle = "Sync Current Test Post Ribbon";
    const testSlug = "sync-current-test-post-ribbon";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post for syncing current post to Ghost via ribbon icon.

## Test Content

Some content to verify the sync works correctly via ribbon.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Click the Ghost sync ribbon icon
    const ribbonClicked = await context.page.evaluate(() => {
      // Look for the Ghost sync ribbon icon
      const ribbonIcons = document.querySelectorAll('.side-dock-ribbon-action, .workspace-ribbon-collapse-btn');
      const ghostIcon = Array.from(ribbonIcons).find(icon => 
        icon.getAttribute('aria-label')?.includes('Ghost') ||
        icon.getAttribute('title')?.includes('Ghost') ||
        icon.textContent?.includes('Ghost')
      );

      if (ghostIcon) {
        (ghostIcon as HTMLElement).click();
        return true;
      }
      return false;
    });

    if (ribbonClicked) {
      console.log("Clicked Ghost sync ribbon icon");
      
      // Wait for success notice to appear
      const successNoticeAppeared = await waitForSuccessNotice(context.page, 5000);
      expect(successNoticeAppeared).toBe(true);

      // Verify sync metadata was updated
      const syncMetadata = await getSyncMetadata(context.page, relativeFilePath);
      expect(syncMetadata).toBeTruthy();
      expect(syncMetadata.ghostId).toBeTruthy();
      expect(syncMetadata.syncedAt).toBeTruthy();

      console.log(`✅ Post synced successfully via ribbon. Ghost ID: ${syncMetadata.ghostId}`);
    } else {
      console.log("⚠️ Ghost sync ribbon icon not found, skipping ribbon test");
      // This is acceptable as ribbon icons may not always be visible
    }
  });

  test("should sync current post to Ghost via direct command", async () => {
    const testTitle = "Sync Current Test Post Direct";
    const testSlug = "sync-current-test-post-direct";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post for syncing current post to Ghost via direct command.

## Test Content

Some content to verify the sync works correctly via direct command.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Execute sync command directly
    const syncResult = await context.page.evaluate(async () => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin) {
        throw new Error('Ghost sync plugin not found');
      }

      try {
        // Execute the sync current post command directly
        await plugin.syncCurrentPost();
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(syncResult.success).toBe(true);

    console.log("Executed sync current post command directly");

    // Wait for success notice to appear
    const successNoticeAppeared = await waitForSuccessNotice(context.page, 5000);
    expect(successNoticeAppeared).toBe(true);

    // Verify sync metadata was updated
    const syncMetadata = await getSyncMetadata(context.page, relativeFilePath);
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata.ghostId).toBeTruthy();
    expect(syncMetadata.syncedAt).toBeTruthy();

    console.log(`✅ Post synced successfully via direct command. Ghost ID: ${syncMetadata.ghostId}`);
  });

  test("should handle posts without slugs", async () => {
    const testTitle = "Post Without Slug";
    const relativeFilePath = `articles/post-without-slug.md`;

    // Create a test post without a slug
    const content = `---
Title: "${testTitle}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post without a slug to verify auto-slug generation.

## Test Content

The system should generate a slug automatically.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Execute sync command directly
    const syncResult = await context.page.evaluate(async () => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin) {
        throw new Error('Ghost sync plugin not found');
      }

      try {
        await plugin.syncCurrentPost();
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    expect(syncResult.success).toBe(true);

    console.log("Executed sync for post without slug");

    // Wait for success notice to appear
    const successNoticeAppeared = await waitForSuccessNotice(context.page, 5000);
    expect(successNoticeAppeared).toBe(true);

    // Verify sync metadata was updated and slug was generated
    const syncMetadata = await getSyncMetadata(context.page, relativeFilePath);
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata.ghostId).toBeTruthy();
    expect(syncMetadata.syncedAt).toBeTruthy();

    console.log(`✅ Post without slug synced successfully. Ghost ID: ${syncMetadata.ghostId}`);
  });

  test("should verify sync metadata is updated after successful sync", async () => {
    const testTitle = "Sync Metadata Test Post";
    const testSlug = "sync-metadata-test-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test post
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Newsletter: null
---

# ${testTitle}

This is a test post to verify sync metadata is properly updated.`;

    await createTestFile(context.page, relativeFilePath, content);
    await openFile(context.page, relativeFilePath);
    await waitForOperation(500);

    // Get initial metadata (should be empty)
    const initialMetadata = await getSyncMetadata(context.page, relativeFilePath);
    expect(initialMetadata).toBeFalsy();

    // Execute sync
    await context.page.evaluate(async () => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      await plugin.syncCurrentPost();
    });

    // Wait for success notice
    const successNoticeAppeared = await waitForSuccessNotice(context.page, 5000);
    expect(successNoticeAppeared).toBe(true);

    // Verify metadata was created and populated
    const finalMetadata = await getSyncMetadata(context.page, relativeFilePath);
    expect(finalMetadata).toBeTruthy();
    expect(finalMetadata.ghostId).toBeTruthy();
    expect(finalMetadata.syncedAt).toBeTruthy();
    expect(new Date(finalMetadata.syncedAt)).toBeInstanceOf(Date);

    console.log(`✅ Sync metadata properly updated. Ghost ID: ${finalMetadata.ghostId}, Synced at: ${finalMetadata.syncedAt}`);
  });
});
