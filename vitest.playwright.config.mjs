import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node', // Use node environment for Playwright tests
    globals: true,
    setupFiles: ['./e2e/setup.ts'], // Only include e2e setup, not the mocked tests/setup.ts
    include: [
      'e2e/**/*playwright*.e2e.ts', // Original Playwright tests
      'e2e/**/ported-*.e2e.ts',     // Ported tests (will use this naming convention)
    ],
    exclude: [
      'tests/**/*',                 // Exclude unit tests
      'e2e/specs/browse-ghost-posts.e2e.ts',     // Legacy CDP tests to exclude
      'e2e/specs/changed-at-handling.e2e.ts',
      'e2e/specs/comprehensive-formatting.e2e.ts',
      'e2e/specs/create-new-post.e2e.ts',
      'e2e/specs/error-handling.e2e.ts',
      'e2e/specs/ghost-roundtrip-sync.e2e.ts',
      'e2e/specs/ghost-sync-e2e.e2e.ts',
      'e2e/specs/post-visibility-updates.e2e.ts',
      'e2e/specs/settings-ui.e2e.ts',
      'e2e/specs/sync-all-posts.e2e.ts',
      'e2e/specs/sync-current-post.e2e.ts',
      'e2e/specs/sync-from-ghost.e2e.ts',
      'e2e/specs/sync-operations.e2e.ts',
      'e2e/specs/underscore-italic-test.e2e.ts',
    ],
    testTimeout: 120000, // 2 minutes for e2e tests
    hookTimeout: 60000,  // 1 minute for setup/teardown hooks (Playwright needs more time)
    // Playwright tests should run sequentially to avoid conflicts
    fileParallelism: false,
    maxConcurrency: 1
  },
  define: {
    global: 'globalThis'
  }
});
